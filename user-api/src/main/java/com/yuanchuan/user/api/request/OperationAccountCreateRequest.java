package com.yuanchuan.user.api.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 运营账号新增请求
 */
@Data
@Schema(description = "运营账号新增请求")
public class OperationAccountCreateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "姓名不能为空")
    @Size(max = 20, message = "姓名不能超过20个字符")
    @Schema(description = "姓名", example = "张三", required = true)
    private String name;

    @NotBlank(message = "员工编码不能为空")
    @Schema(description = "员工编码", example = "EMP001", required = true)
    private String employeeCode;

    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^09\\d{8}$", message = "请输入正确的台湾手机号码")
    @Schema(description = "台湾手机号，以09开头，长度10位", example = "**********", required = true)
    private String phone;

    @NotEmpty(message = "角色不能为空")
    @Schema(description = "角色ID列表，支持多选", example = "[1, 2, 3]", required = true)
    private List<Long> roleIds;
}
