//package com.yuanchuan.user.api.controller;
//
//import com.yuanchuan.common.dto.Result;
//import com.yuanchuan.common.response.PageResult;
//import com.yuanchuan.user.api.dto.PermissionDTO;
//import com.yuanchuan.user.api.dto.PermissionSelectDTO;
//import com.yuanchuan.user.api.dto.PermissionTreeDTO;
//import com.yuanchuan.user.api.request.PermissionCreateRequest;
//import com.yuanchuan.user.api.request.PermissionQueryRequest;
//import com.yuanchuan.user.api.request.PermissionUpdateRequest;
//import com.yuanchuan.user.api.service.PermissionService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import java.util.List;
//
///**
// * 权限管理控制器
// */
//@Slf4j
//@RestController
//@RequestMapping("/api/permissions")
//@Tag(name = "权限管理", description = "权限管理相关接口")
//public class PermissionController {
//
//    @Autowired
//    private PermissionService permissionService;
//
//    @GetMapping("/user/{accountId}")
//    @Operation(summary = "根据账户ID查询用户权限树", description = "查询指定账户的权限树结构")
//    public Result<List<PermissionTreeDTO>> getUserPermissionTree(
//            @Parameter(description = "账户ID") @PathVariable Long accountId) {
//        List<PermissionTreeDTO> permissions = permissionService.getUserPermissionTree(accountId);
//        return Result.success(permissions);
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "分页查询权限列表", description = "根据条件分页查询权限列表")
//    public Result<PageResult<PermissionDTO>> queryPermissions(@Valid PermissionQueryRequest request) {
//        PageResult<PermissionDTO> result = permissionService.queryPermissions(request);
//        return Result.success(result);
//    }
//
//    @GetMapping("/entries")
//    @Operation(summary = "获取权限入口下拉列表", description = "获取类型为MENU的权限作为入口选项")
//    public Result<List<PermissionSelectDTO>> getPermissionEntries() {
//        List<PermissionSelectDTO> entries = permissionService.getPermissionEntries();
//        return Result.success(entries);
//    }
//
//    @PostMapping
//    @Operation(summary = "创建权限", description = "创建新的权限")
//    public Result<Long> createPermission(@Valid @RequestBody PermissionCreateRequest request) {
//        Long permissionId = permissionService.createPermission(request);
//        return Result.success(permissionId);
//    }
//
//    @PutMapping
//    @Operation(summary = "更新权限", description = "更新权限信息")
//    public Result<Boolean> updatePermission(@Valid @RequestBody PermissionUpdateRequest request) {
//        Boolean success = permissionService.updatePermission(request);
//        return Result.success(success);
//    }
//
//    @GetMapping("/{id}")
//    @Operation(summary = "根据ID查询权限详情", description = "查询指定ID的权限详细信息")
//    public Result<PermissionDTO> getPermissionById(
//            @Parameter(description = "权限ID") @PathVariable Long id) {
//        PermissionDTO permission = permissionService.getPermissionById(id);
//        return Result.success(permission);
//    }
//
//    @DeleteMapping("/{id}")
//    @Operation(summary = "删除权限", description = "删除指定ID的权限")
//    public Result<Boolean> deletePermission(
//            @Parameter(description = "权限ID") @PathVariable Long id) {
//        Boolean success = permissionService.deletePermission(id);
//        return Result.success(success);
//    }
//}
