package com.yuanchuan.user.api.service;

import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.user.api.dto.RoleDTO;
import com.yuanchuan.user.api.dto.RoleSelectDTO;
import com.yuanchuan.user.api.request.RoleCreateRequest;
import com.yuanchuan.user.api.request.RoleQueryRequest;
import com.yuanchuan.user.api.request.RoleUpdateRequest;

import java.util.List;

/**
 * 角色管理服务接口
 */
public interface RoleService {

    /**
     * 分页查询角色列表
     *
     * @param request 查询请求
     * @return 角色分页结果
     */
    PageResult<RoleDTO> queryRoles(RoleQueryRequest request);

    /**
     * 获取角色下拉列表
     *
     * @return 角色下拉列表
     */
    List<RoleSelectDTO> getRoleSelectList();

    /**
     * 创建角色
     *
     * @param request 创建请求
     * @return 角色ID
     */
    Long createRole(RoleCreateRequest request);

    /**
     * 更新角色
     *
     * @param request 更新请求
     * @return 是否成功
     */
    Boolean updateRole(RoleUpdateRequest request);

    /**
     * 根据ID查询角色详情
     *
     * @param id 角色ID
     * @return 角色详情
     */
    RoleDTO getRoleById(Long id);

    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 是否成功
     */
    Boolean deleteRole(Long id);

    /**
     * 根据账户ID查询用户角色列表
     *
     * @param accountId 账户ID
     * @return 角色列表
     */
    List<RoleDTO> getUserRoles(Long accountId);
}
