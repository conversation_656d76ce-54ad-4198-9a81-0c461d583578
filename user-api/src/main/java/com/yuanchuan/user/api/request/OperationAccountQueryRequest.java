package com.yuanchuan.user.api.request;

import com.yuanchuan.common.domain.query.PageQueryV;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运营账号查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "运营账号查询请求")
public class OperationAccountQueryRequest extends PageQueryV {
    private static final long serialVersionUID = 1L;

    @Schema(description = "角色ID，支持多个角色查询")
    private String roleIds;

    @Schema(description = "账户状态：1-正常，2-锁定，3-禁用")
    private Integer accountStatus;

    @Schema(description = "关键词搜索（支持姓名、邮箱、手机号）")
    private String keyword;
}
