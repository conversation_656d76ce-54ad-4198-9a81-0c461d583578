//package com.yuanchuan.user.api.controller;
//
//import com.yuanchuan.common.dto.Result;
//import com.yuanchuan.common.response.PageResult;
//import com.yuanchuan.user.api.dto.RoleDTO;
//import com.yuanchuan.user.api.dto.RoleSelectDTO;
//import com.yuanchuan.user.api.request.RoleCreateRequest;
//import com.yuanchuan.user.api.request.RoleQueryRequest;
//import com.yuanchuan.user.api.request.RoleUpdateRequest;
//import com.yuanchuan.user.api.service.RoleService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import java.util.List;
//
///**
// * 角色管理控制器
// */
//@Slf4j
//@RestController
//@RequestMapping("/api/roles")
//@Tag(name = "角色管理", description = "角色管理相关接口")
//public class RoleController {
//
//    @Autowired
//    private RoleService roleService;
//
//    @GetMapping("/list")
//    @Operation(summary = "分页查询角色列表", description = "根据条件分页查询角色列表")
//    public Result<PageResult<RoleDTO>> queryRoles(@Valid RoleQueryRequest request) {
//        PageResult<RoleDTO> result = roleService.queryRoles(request);
//        return Result.success(result);
//    }
//
//    @GetMapping("/select")
//    @Operation(summary = "获取角色下拉列表", description = "获取所有有效角色的下拉选项")
//    public Result<List<RoleSelectDTO>> getRoleSelectList() {
//        List<RoleSelectDTO> roles = roleService.getRoleSelectList();
//        return Result.success(roles);
//    }
//
//    @PostMapping
//    @Operation(summary = "创建角色", description = "创建新的角色")
//    public Result<Long> createRole(@Valid @RequestBody RoleCreateRequest request) {
//        Long roleId = roleService.createRole(request);
//        return Result.success(roleId);
//    }
//
//    @PutMapping
//    @Operation(summary = "更新角色", description = "更新角色信息")
//    public Result<Boolean> updateRole(@Valid @RequestBody RoleUpdateRequest request) {
//        Boolean success = roleService.updateRole(request);
//        return Result.success(success);
//    }
//
//    @GetMapping("/{id}")
//    @Operation(summary = "根据ID查询角色详情", description = "查询指定ID的角色详细信息")
//    public Result<RoleDTO> getRoleById(
//            @Parameter(description = "角色ID") @PathVariable Long id) {
//        RoleDTO role = roleService.getRoleById(id);
//        return Result.success(role);
//    }
//
//    @DeleteMapping("/{id}")
//    @Operation(summary = "删除角色", description = "删除指定ID的角色")
//    public Result<Boolean> deleteRole(
//            @Parameter(description = "角色ID") @PathVariable Long id) {
//        Boolean success = roleService.deleteRole(id);
//        return Result.success(success);
//    }
//
//    @GetMapping("/user/{accountId}")
//    @Operation(summary = "根据账户ID查询用户角色", description = "查询指定账户的角色列表")
//    public Result<List<RoleDTO>> getUserRoles(
//            @Parameter(description = "账户ID") @PathVariable Long accountId) {
//        List<RoleDTO> roles = roleService.getUserRoles(accountId);
//        return Result.success(roles);
//    }
//}
