package com.yuanchuan.user.api.controller;

import com.yuanchuan.common.dto.Result;
import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.user.api.dto.OperationAccountDTO;
import com.yuanchuan.user.api.request.OperationAccountCreateRequest;
import com.yuanchuan.user.api.request.OperationAccountQueryRequest;
import com.yuanchuan.user.api.request.OperationAccountUpdateRequest;
import com.yuanchuan.user.api.service.OperationAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 运营账号管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/operation-accounts")
@Tag(name = "运营账号管理", description = "运营账号管理相关接口")
public class OperationAccountController {

    @Autowired
    private OperationAccountService operationAccountService;

    @GetMapping("/list")
    @Operation(summary = "分页查询运营账号列表", description = "根据条件分页查询运营账号列表")
    public Result<PageResult<OperationAccountDTO>> queryOperationAccounts(@Valid OperationAccountQueryRequest request) {
        PageResult<OperationAccountDTO> result = operationAccountService.queryOperationAccounts(request);
        return Result.success(result);
    }

    @PostMapping
    @Operation(summary = "新增运营账号", description = "创建新的运营账号")
    public Result<Long> createOperationAccount(@Valid @RequestBody OperationAccountCreateRequest request) {
        Long accountId = operationAccountService.createOperationAccount(request);
        return Result.success(accountId);
    }

    @PutMapping
    @Operation(summary = "修改运营账号", description = "修改运营账号信息")
    public Result<Boolean> updateOperationAccount(@Valid @RequestBody OperationAccountUpdateRequest request) {
        Boolean success = operationAccountService.updateOperationAccount(request);
        return Result.success(success);
    }

    @GetMapping("/{id}")
    @Operation(summary = "查询运营账号详情", description = "根据ID查询运营账号详情")
    public Result<OperationAccountDTO> getOperationAccountById(
            @Parameter(description = "账号ID", required = true) @PathVariable Long id) {
        OperationAccountDTO account = operationAccountService.getOperationAccountById(id);
        return Result.success(account);
    }
}
