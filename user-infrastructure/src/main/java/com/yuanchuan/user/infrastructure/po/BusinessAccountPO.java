package com.yuanchuan.user.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商户账户表持久化对象
 */
@Data
@TableName("business_account")
public class BusinessAccountPO {
    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 自然人ID
     */
    private Long userPersonId;
    
    /**
     * 账户名称
     */
    private String accountName;
    
    /**
     * 密码哈希
     */
    private String password;
    
    /**
     * 账户类型 SHOP-商户 、 OPERATION-运营
     */
    private Integer accountType;
    
    /**
     * 账户状态
     */
    private Integer accountStatus;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Boolean active;

    /**
     * 组织ID
     */
    private Long orgId;
}
