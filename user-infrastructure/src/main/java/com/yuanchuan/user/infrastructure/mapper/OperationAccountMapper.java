package com.yuanchuan.user.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.user.infrastructure.po.BusinessAccountPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 运营账号数据访问Mapper
 */
@Mapper
public interface OperationAccountMapper extends BaseMapper<BusinessAccountPO> {

    /**
     * 查询运营账号列表（关联用户信息和角色信息）
     *
     * @param roleIds 角色ID列表，多个用逗号分隔
     * @param accountStatus 账户状态
     * @param keyword 关键词搜索
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 账号列表
     */
    List<BusinessAccountPO> queryOperationAccountsWithUserInfo(
            @Param("roleIds") String roleIds,
            @Param("accountStatus") Integer accountStatus,
            @Param("keyword") String keyword,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );

    /**
     * 统计运营账号数量
     *
     * @param roleIds 角色ID列表，多个用逗号分隔
     * @param accountStatus 账户状态
     * @param keyword 关键词搜索
     * @return 总数量
     */
    Long countOperationAccountsWithUserInfo(
            @Param("roleIds") String roleIds,
            @Param("accountStatus") Integer accountStatus,
            @Param("keyword") String keyword
    );

    /**
     * 根据邮箱和账户类型查询账户
     *
     * @param email 邮箱
     * @param accountType 账户类型
     * @return 账户信息
     */
    @Select("SELECT ba.* FROM business_account ba " +
            "INNER JOIN user_person up ON ba.user_person_id = up.id " +
            "WHERE up.email = #{email} AND ba.account_type = #{accountType} AND ba.active = 1")
    BusinessAccountPO findByEmailAndAccountType(@Param("email") String email, @Param("accountType") Integer accountType);

    /**
     * 根据手机号和账户类型查询账户
     *
     * @param phone 手机号
     * @param accountType 账户类型
     * @return 账户信息
     */
    @Select("SELECT ba.* FROM business_account ba " +
            "INNER JOIN user_person up ON ba.user_person_id = up.id " +
            "WHERE up.phone = #{phone} AND ba.account_type = #{accountType} AND ba.active = 1")
    BusinessAccountPO findByPhoneAndAccountType(@Param("phone") String phone, @Param("accountType") Integer accountType);
}
