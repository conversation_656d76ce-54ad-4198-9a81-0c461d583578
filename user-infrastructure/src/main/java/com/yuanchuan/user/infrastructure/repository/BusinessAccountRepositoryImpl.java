package com.yuanchuan.user.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.user.domain.model.BusinessAccount;
import com.yuanchuan.user.domain.repository.BusinessAccountRepository;
import com.yuanchuan.user.infrastructure.mapper.BusinessAccountMapper;
import com.yuanchuan.user.infrastructure.mapper.OperationAccountMapper;
import com.yuanchuan.user.infrastructure.po.BusinessAccountPO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 商户账户仓储实现类
 */
@Repository
public class BusinessAccountRepositoryImpl implements BusinessAccountRepository {

    @Autowired
    private BusinessAccountMapper businessAccountMapper;

    @Autowired
    private OperationAccountMapper operationAccountMapper;

    @Override
    public BusinessAccount save(BusinessAccount account) {
        BusinessAccountPO po = toPO(account);
        if (po.getId() == null) {
            businessAccountMapper.insert(po);
        } else {
            businessAccountMapper.updateById(po);
        }
        return toEntity(po);
    }

    @Override
    public Optional<BusinessAccount> findById(Long id) {
        LambdaQueryWrapper<BusinessAccountPO> query = new LambdaQueryWrapper<>();
        query.eq(BusinessAccountPO::getId, id)
                .eq(BusinessAccountPO::getActive, 1); // 只查未删除
        BusinessAccountPO po = businessAccountMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<BusinessAccount> findByUserPersonId(Long userPersonId) {
        LambdaQueryWrapper<BusinessAccountPO> query = new LambdaQueryWrapper<>();
        query.eq(BusinessAccountPO::getUserPersonId, userPersonId)
                .eq(BusinessAccountPO::getActive, 1); // 只查未删除
        BusinessAccountPO po = businessAccountMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<BusinessAccount> findByAccountNameAndAccountType(String accountName, Integer accountType) {
        LambdaQueryWrapper<BusinessAccountPO> query = new LambdaQueryWrapper<>();
        query.eq(BusinessAccountPO::getAccountName, accountName)
                .eq(BusinessAccountPO::getAccountType, accountType)
                .eq(BusinessAccountPO::getActive, 1); // 只查未删除
        BusinessAccountPO po = businessAccountMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public boolean updateAccountStatus(Long id, Integer accountStatus) {
        BusinessAccountPO po = new BusinessAccountPO();
        po.setId(id);
        po.setAccountStatus(accountStatus);
        return businessAccountMapper.updateById(po) > 0;
    }

    @Override
    public boolean updatePassword(Long id, String password) {
        BusinessAccountPO po = new BusinessAccountPO();
        po.setId(id);
        po.setPassword(password);
        return businessAccountMapper.updateById(po) > 0;
    }

    @Override
    public boolean updateAccountName(Long id, String accountName) {
        BusinessAccountPO po = new BusinessAccountPO();
        po.setId(id);
        po.setAccountName(accountName);
        return businessAccountMapper.updateById(po) > 0;
    }

    /**
     * 将领域对象转换为持久化对象
     */
    private BusinessAccountPO toPO(BusinessAccount account) {
        BusinessAccountPO po = new BusinessAccountPO();
        BeanUtils.copyProperties(account, po);
        return po;
    }

    @Override
    public List<BusinessAccount> queryOperationAccounts(String roleIds, Integer accountStatus, String keyword, PageQueryV pageQuery) {
        Integer offset = null;
        Integer limit = null;

        if (pageQuery != null) {
            offset = (pageQuery.getPageNo() - 1) * pageQuery.getPageSize();
            limit = pageQuery.getPageSize();
        }

        List<BusinessAccountPO> pos = operationAccountMapper.queryOperationAccountsWithUserInfo(
                roleIds, accountStatus, keyword, offset, limit);
        return pos.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public Long countOperationAccounts(String roleIds, Integer accountStatus, String keyword) {
        return operationAccountMapper.countOperationAccountsWithUserInfo(roleIds, accountStatus, keyword);
    }

    @Override
    public Optional<BusinessAccount> findByEmailAndAccountType(String email, Integer accountType) {
        BusinessAccountPO po = operationAccountMapper.findByEmailAndAccountType(email, accountType);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<BusinessAccount> findByPhoneAndAccountType(String phone, Integer accountType) {
        BusinessAccountPO po = operationAccountMapper.findByPhoneAndAccountType(phone, accountType);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    /**
     * 将持久化对象转换为领域对象
     */
    private BusinessAccount toEntity(BusinessAccountPO po) {
        BusinessAccount account = new BusinessAccount();
        BeanUtils.copyProperties(po, account);
        return account;
    }
}
