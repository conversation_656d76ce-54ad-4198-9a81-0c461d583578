package com.yuanchuan.user.domain.service.impl;

import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.domain.model.BusinessAccountRole;
import com.yuanchuan.user.domain.model.Role;
import com.yuanchuan.user.domain.model.RolePermission;
import com.yuanchuan.user.domain.repository.BusinessAccountRepository;
import com.yuanchuan.user.domain.repository.BusinessAccountRoleRepository;
import com.yuanchuan.user.domain.repository.RolePermissionRepository;
import com.yuanchuan.user.domain.repository.RoleRepository;
import com.yuanchuan.user.domain.service.RoleDomainService;
import com.yuanchuan.user.domain.service.AuditLogDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色领域服务实现类
 */
@Slf4j
@Service
public class RoleDomainServiceImpl implements RoleDomainService {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private RolePermissionRepository rolePermissionRepository;

    @Autowired
    private AuditLogDomainService auditLogDomainService;

    @Autowired
    private BusinessAccountRoleRepository businessAccountRoleRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Role createRole(Role role, List<Long> permissionIds,String createBy) {
        // 校验角色名称唯一性
        if (!checkRoleNameUnique(role.getRoleName(),null)) {
            throw new BusinessException(UsersErrorCode.ROLE_NAME_EXIST.getCode(), UsersErrorCode.ROLE_NAME_EXIST.getMsg());
        }

        if (!checkRoleCodeUnique(role.getRoleCode(),null)) {
            throw new BusinessException(UsersErrorCode.ROLE_CODE_EXIST.getCode(), UsersErrorCode.ROLE_CODE_EXIST.getMsg());
        }

        // 生成角色编码
        //role.setRoleCode(generateRoleCode(role.getRoleName()));

        // 设置默认值
        role.setStatus(1); // 默认启用
        role.setActive(1); // 未删除
        role.setCreatedAt(LocalDateTime.now());
        role.setUpdatedAt(LocalDateTime.now());
        role.setCreatedBy(createBy);
        role.setUpdatedBy(createBy);

        // 保存角色
        Role savedRole = roleRepository.save(role);

        // 保存角色权限关联
        if (!CollectionUtils.isEmpty(permissionIds)) {
            saveRolePermissions(savedRole.getId(), permissionIds,createBy);
        }

        return savedRole;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Role updateRole(Role role, List<Long> permissionIds,Long createId,String createBy) {
        // 校验角色是否存在
        Role existingRole = getRoleById(role.getId());

        // 记录变更前的数据
        Map<String, Object> oldValues = new HashMap<>();
        oldValues.put("role_name", existingRole.getRoleName());
        oldValues.put("description", existingRole.getDescription());
        oldValues.put("status", existingRole.getStatus());

        // 校验角色名称唯一性
        if (!checkRoleNameUnique(role.getRoleName(),role.getId())) {
            throw new BusinessException(UsersErrorCode.ROLE_NAME_EXIST.getCode(), UsersErrorCode.ROLE_NAME_EXIST.getMsg());
        }

        if (!checkRoleCodeUnique(role.getRoleCode(),role.getId())) {
            throw new BusinessException(UsersErrorCode.ROLE_CODE_EXIST.getCode(), UsersErrorCode.ROLE_CODE_EXIST.getMsg());
        }

        // 更新字段
        existingRole.setRoleName(role.getRoleName());
        existingRole.setDescription(role.getDescription());
        if (role.getStatus() != null) {
            existingRole.setStatus(role.getStatus());
        }
        existingRole.setUpdatedAt(LocalDateTime.now());
        existingRole.setUpdatedBy(createBy);

        // 保存角色
        Role savedRole = roleRepository.save(existingRole);

        // 更新角色权限关联
        if (permissionIds != null) {
            // 删除原有关联
            rolePermissionRepository.deleteByRoleId(savedRole.getId());
            // 保存新关联
            if (!CollectionUtils.isEmpty(permissionIds)) {
                saveRolePermissions(savedRole.getId(), permissionIds,createBy);
            }
        }

        // 记录变更日志
        Map<String, Object> newValues = new HashMap<>();
        newValues.put("role_name", savedRole.getRoleName());
        newValues.put("description", savedRole.getDescription());
        newValues.put("status", savedRole.getStatus());

        // 记录审计日志
        auditLogDomainService.recordAuditLog(
                "ROLE",
                savedRole.getId(),
                "UPDATE",
                createId,
                createBy,
                oldValues,
                newValues,
                "更新角色信息"
        );

        return savedRole;
    }

    @Override
    public Role getRoleById(Long id) {
        Optional<Role> role = roleRepository.findById(id);
        if (!role.isPresent()) {
            throw new BusinessException(UsersErrorCode.ROLE_NOT_EXIST.getCode(), UsersErrorCode.ROLE_NOT_EXIST.getMsg());
        }
        return role.get();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteRole(Long id,String createBy) {
        // 校验角色是否存在
        Role role = getRoleById(id);

        // 校验是否有用户关联此角色，如果有则不允许删除
        List<BusinessAccountRole> accountRoleList =  businessAccountRoleRepository.findByRoleId(role.getId());
        if(!CollectionUtils.isEmpty(accountRoleList)){
            throw new BusinessException(UsersErrorCode.ROLE_RELATED_USER_NOT_ALLOWED_TO_DELETE.getCode(), UsersErrorCode.ROLE_RELATED_USER_NOT_ALLOWED_TO_DELETE.getMsg());
        }
        // 删除角色权限关联
        rolePermissionRepository.deleteByRoleId(id);

        // 逻辑删除角色
        role.setActive(0);
        role.setUpdatedAt(LocalDateTime.now());
        role.setUpdatedBy(createBy);
        roleRepository.save(role);

        return true;
    }

    @Override
    public List<Role> getUserRoles(Long accountId) {
        return roleRepository.findByBusinessAccountId(accountId);
    }

    @Override
    public List<Role> findByBusinessAccountId(Long businessAccountId) {
        return roleRepository.findByBusinessAccountId(businessAccountId);
    }

    @Override
    public List<Role> queryRoles(String roleId, Integer status, String keyword, PageQueryV pageQuery) {
        return roleRepository.findByConditions(roleId, status, keyword, pageQuery);
    }

    @Override
    public Long countRoles(String roleId, Integer status, String keyword) {
        return roleRepository.countByConditions(roleId, status, keyword);
    }

    @Override
    public List<Role> getRoleSelectList() {
        return roleRepository.findAll().stream()
                .filter(role -> role.getStatus() == 1 && role.getActive() == 1)
                .collect(Collectors.toList());
    }

    @Override
    public Boolean checkRoleNameUnique(String roleName, Long excludeId) {
        if (!StringUtils.hasText(roleName)) {
            return true;
        }

        // 查询是否存在同名角色
        List<Role> allRoles = roleRepository.findAll();
        for (Role role : allRoles) {
            if (role.getRoleName().equals(roleName)) {
                // 如果是更新操作且是同一个角色，则允许
                if (excludeId != null && role.getId().equals(excludeId)) {
                    continue;
                }
                return false; // 存在重名角色
            }
        }
        return true;
    }

    @Override
    public Boolean checkRoleCodeUnique(String roleCode, Long excludeId) {
        if (!StringUtils.hasText(roleCode)) {
            return true;
        }

        // 查询是否存在同名角色
        List<Role> allRoles = roleRepository.findAll();
        for (Role role : allRoles) {
            if (role.getRoleCode().equals(roleCode)) {
                // 如果是更新操作且是同一个角色，则允许
                if (excludeId != null && role.getId().equals(excludeId)) {
                    continue;
                }
                return false; // 存在重名角色code
            }
        }
        return true;
    }

    @Override
    public List<Long> getPermissionIdsByRoleId(Long roleId) {
        List<RolePermission> rolePermissions = rolePermissionRepository.findByRoleId(roleId);
        return rolePermissions.stream()
                .map(RolePermission::getPermissionId)
                .collect(Collectors.toList());
    }

    @Override
    public Role queryRoleById(Long id) {
        return roleRepository.queryRoleById(id);
    }


    /**
     * 生成角色编码
     */
    private String generateRoleCode(String roleName) {
        // 简单实现：使用时间戳 + 随机数
        return "ROLE_" + System.currentTimeMillis();
    }

    /**
     * 保存角色权限关联
     */
    private void saveRolePermissions(Long roleId, List<Long> permissionIds,String createBy) {
        List<RolePermission> rolePermissions = permissionIds.stream()
                .map(permissionId -> {
                    RolePermission rolePermission = new RolePermission();
                    rolePermission.setRoleId(roleId);
                    rolePermission.setPermissionId(permissionId);
                    rolePermission.setGrantType("GRANT");
                    rolePermission.setActive(1);
                    rolePermission.setCreatedAt(new Date());
                    rolePermission.setUpdatedAt(new Date());
                    rolePermission.setCreatedBy(createBy);
                    rolePermission.setUpdatedBy(createBy);
                    return rolePermission;
                })
                .collect(Collectors.toList());

        rolePermissionRepository.batchSave(rolePermissions);
    }
}
