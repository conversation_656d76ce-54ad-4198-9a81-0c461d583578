package com.yuanchuan.user.domain.repository;

import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.user.domain.model.BusinessAccount;

import java.util.List;
import java.util.Optional;

/**
 * 商户账户仓储接口
 */
public interface BusinessAccountRepository {
    /**
     * 保存商户账户信息
     *
     * @param account 商户账户信息
     * @return 保存后的商户账户信息
     */
    BusinessAccount save(BusinessAccount account);

    /**
     * 根据ID查询商户账户
     *
     * @param id 商户账户ID
     * @return 商户账户信息
     */
    Optional<BusinessAccount> findById(Long id);

    /**
     * 根据自然人ID查询商户账户
     *
     * @param userPersonId 自然人ID
     * @return 商户账户信息
     */
    Optional<BusinessAccount> findByUserPersonId(Long userPersonId);

    /**
     * 根据账号名称和账号类型查询商户账户
     *
     * @param accountName 账号名称
     * @param accountType 账号类型
     * @return 商户账户信息
     */
    Optional<BusinessAccount> findByAccountNameAndAccountType(String accountName, Integer accountType);

    /**
     * 更新账户状态
     *
     * @param id 商户账户ID
     * @param accountStatus 账户状态
     * @return 是否更新成功
     */
    boolean updateAccountStatus(Long id, Integer accountStatus);

    /**
     * 更新密码
     *
     * @param id 商户账户ID
     * @param password 密码哈希
     * @return 是否更新成功
     */
    boolean updatePassword(Long id, String password);

    /**
     * 更新账户名称
     *
     * @param id 商户账户ID
     * @param accountName 账户名称
     * @return 是否更新成功
     */
    boolean updateAccountName(Long id, String accountName);

    /**
     * 查询运营账号列表
     *
     * @param roleIds 角色ID列表，多个用逗号分隔
     * @param accountStatus 账户状态
     * @param keyword 关键词搜索
     * @param pageQuery 分页参数
     * @return 账号列表
     */
    List<BusinessAccount> queryOperationAccounts(String roleIds, Integer accountStatus, String keyword, PageQueryV pageQuery);

    /**
     * 统计运营账号数量
     *
     * @param roleIds 角色ID列表，多个用逗号分隔
     * @param accountStatus 账户状态
     * @param keyword 关键词搜索
     * @return 总数量
     */
    Long countOperationAccounts(String roleIds, Integer accountStatus, String keyword);

    /**
     * 根据邮箱和账户类型查询账户（用于唯一性验证）
     *
     * @param email 邮箱
     * @param accountType 账户类型
     * @return 账户信息
     */
    Optional<BusinessAccount> findByEmailAndAccountType(String email, Integer accountType);

    /**
     * 根据手机号和账户类型查询账户（用于唯一性验证）
     *
     * @param phone 手机号
     * @param accountType 账户类型
     * @return 账户信息
     */
    Optional<BusinessAccount> findByPhoneAndAccountType(String phone, Integer accountType);
}
