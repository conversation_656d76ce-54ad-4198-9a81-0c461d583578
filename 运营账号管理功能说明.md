# 运营账号管理功能

## 功能概述

根据需求实现运营账号管理功能，包括账号列表查询、新增、修改等功能。运营账户表是 business_account，字段类型是 account_type = OPERATION (值为2)。

## 功能特性

### 5.1 账号列表
- 显示字段：姓名、员工编码、邮箱、手机号、角色、状态、更新人
- 支持分页查询

### 5.2 条件查询
- 角色筛选：支持按角色ID筛选
- 状态筛选：支持按账户状态筛选（1-正常，2-锁定，3-禁用）
- 关键词搜索：支持姓名、邮箱、手机号模糊搜索

### 5.3 账号新增
- **姓名**：必填，不超过20个字符
- **员工编码**：必填，系统唯一性校验
- **邮箱**：必填，格式校验，唯一性校验
- **手机号**：必填，台湾手机号格式校验（09开头10位），唯一性校验
- **角色选择**：必填，支持多选，只能选择有效状态的角色
- **创建成功后**：记录第一条更新记录，账号设为有效状态

### 5.4 修改账号
- 支持修改：姓名、邮箱、手机号、关联角色
- 业务规则：仅"有效状态的角色"可被"有效状态的账号"应用相关权限

## 技术架构

### DDD分层架构
```
user-api/           # API层 - 对外接口
user-application/   # 应用层 - 业务流程编排
user-domain/        # 领域层 - 核心业务逻辑
user-infrastructure/ # 基础设施层 - 技术实现
```

### 核心文件

#### API层 (user-api)
- `OperationAccountController.java` - 运营账号管理控制器
- `OperationAccountQueryRequest.java` - 查询请求
- `OperationAccountCreateRequest.java` - 新增请求
- `OperationAccountUpdateRequest.java` - 修改请求
- `OperationAccountDTO.java` - 响应DTO
- `OperationAccountService.java` - 服务接口

#### 应用层 (user-application)
- `OperationAccountApplicationService.java` - 应用服务接口
- `OperationAccountApplicationServiceImpl.java` - 应用服务实现
- `OperationAccountServiceImpl.java` - Dubbo服务实现

#### 领域层 (user-domain)
- `OperationAccountDomainService.java` - 领域服务接口
- `OperationAccountDomainServiceImpl.java` - 领域服务实现

#### 基础设施层 (user-infrastructure)
- `OperationAccountMapper.java` - 数据访问Mapper
- `OperationAccountMapper.xml` - SQL映射文件

## 数据库设计

### 主要表结构
- `business_account` - 商户账户表（运营账户 account_type=2）
- `user_person` - 自然人表（存储基本信息）
- `business_account_role` - 账户角色关联表
- `role` - 角色表
- `user_change_logs` - 用户变更日志表

### 关键字段
- `business_account.account_type = 2` - 运营账户类型
- `business_account.account_name` - 员工编码
- `business_account.account_status` - 账户状态（1-正常，2-锁定，3-禁用）

## API接口

### 1. 查询运营账号列表
```
GET /api/operation-accounts/list
```

### 2. 新增运营账号
```
POST /api/operation-accounts
```

### 3. 修改运营账号
```
PUT /api/operation-accounts
```

### 4. 查询运营账号详情
```
GET /api/operation-accounts/{id}
```

## 业务规则

### 验证规则
1. **姓名**：1-20个字符
2. **员工编码**：系统内唯一
3. **邮箱**：标准邮箱格式，系统内唯一
4. **手机号**：台湾手机号格式（09开头10位），系统内唯一
5. **角色**：只能选择有效状态的角色

### 变更日志
- 创建账号时记录初始状态
- 修改账号时记录变更前后的值
- 日志格式：JSON对象，字段名作为key

### 权限控制
- 只有有效状态的角色才能被分配给账号
- 只有有效状态的账号才能应用角色权限

## 使用说明

1. **部署**：将代码部署到对应的模块目录
2. **数据库**：确保相关表结构已创建
3. **配置**：检查Dubbo服务配置
4. **测试**：建议编写单元测试验证功能

## 注意事项

1. 代码遵循现有的DDD架构模式
2. 使用现有的分页、验证、异常处理机制
3. 集成现有的用户变更日志功能
4. 保持与现有代码风格一致
