apiVersion: v1
kind: ConfigMap
metadata:
  name: user-config
  namespace: default
data:
  application.yml: |
    server:
      port: 8082
    
    spring:
      main:
        allow-bean-definition-overriding: true
      datasource:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ***************************************************************************************************************
        username: frchuser
        password: Frch2025@Dev
      data:
        redis:
          host: frch-redis-dev.redis.cache.windows.net
          port: 6380
          username: default
          password: fX9W6BYENhvxUfaqRIV4rAlyvHVkKhvvPAzCaMMMpWk=
          database: 0
          ssl:
            enabled: true
          timeout: 10000
          lettuce:
            pool:
              max-active: 8
              max-wait: -1
              max-idle: 8
              min-idle: 0
            shutdown-timeout: 100ms

    # Zookeeper配置
    zookeeper:
      address: zookeeper-0.zookeeper.default.svc.cluster.local:2181,zookeeper-1.zookeeper.default.svc.cluster.local:2181
      name: com.yuanchuan.user

    # Dubbo配置
    dubbo:
      application:
        name: com.yuanchuan.user
        # 启用QoS功能
        qos-enable: true
        qos-port: 22222
        qos-accept-foreign-ip: false
      protocol:
        name: dubbo
        port: 20801
        host: 0.0.0.0
      scan:
        base-packages: com.yuanchuan.user
      registry:
        address: zookeeper://zookeeper-0.zookeeper.default.svc.cluster.local:2181,zookeeper-1.zookeeper.default.svc.cluster.local:2181
        register: true
        timeout: 60000
        parameters:
          blockUntilConnectedWait: 60000
          retryIntervalMillis: 5000
          retryTimes: 5
          sessionTimeoutMs: 180000
          connectionTimeoutMs: 30000
        client: curator
      group: user-dev
      provider:
        payload: 83886080
      consumer:
        timeout: 600000
        check: false

    # MyBatis-Plus配置
    mybatis-plus:
      mapper-locations: classpath*:mapper/**/*.xml
      type-aliases-package: com.yuanchuan.user.dto
      configuration:
        map-underscore-to-camel-case: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

    # 日志配置
    logging:
      level:
        org.apache.dubbo: DEBUG
        org.apache.zookeeper: DEBUG
        # 登录日志相关类的日志级别配置
        com.yuanchuan.user.application.aspect.LoginLogAspect: DEBUG
        com.yuanchuan.user.application.service.impl.LoginLogApplicationServiceImpl: DEBUG
        com.yuanchuan.user.domain.service.impl.LoginLogDomainServiceImpl: DEBUG

    # 第三方登录配置
    justauth:
      enabled: true
      extend:
        enum-class: com.yuanchuan.user.domain.auth.FetnetAuthSource
        config:
          TEST:
            request-class: com.yuanchuan.user.domain.auth.FetnetAuthRequest
            client-id: xxxxxx
            client-secret: xxxxxxxx
            redirect-uri: http://oauth.xkcoding.com/demo/oauth/test/callback
      type:
        GOOGLE:
          client-id: 172554427456-6hq3n9qa8dtpv3v093bdvl1g9bvsquk3.apps.googleusercontent.com
          client-secret: 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
          redirect-uri: http://localhost:8091/api/v1/google/callback
        APPLE:
          client-id: 10**********6
          client-secret: 1f7d08**********5b7**********29e
          redirect-uri: http://localhost:8091/api/v1/google/callback
        LINE:
          client-id: 2007426546
          client-secret: 2193b13d2194d19301fa7813eadd869f
          redirect-uri: http://localhost:8091/api/v1/line/callback

    # 心生活配置
    fetnet:
      base-url: https://login2-dev.fetnet.net
      authorize-url: ${fetnet.base-url}/mga/sps/oauth/oauth20/authorize
      token-url: ${fetnet.base-url}/mga/sps/oauth/oauth20/token
      revoke-url: ${fetnet.base-url}/mga/sps/oauth/oauth20/revoke

    # 登录日志功能配置
    app:
      login-log:
        enabled: true
        aop-enabled: true
        async: true
        record-success: true
        record-failure: true
        ip-location-enabled: true
        abnormal-threshold: 5
        abnormal-time-window: 1
        retention-days: 30
        thread-pool:
          core-size: 4
          max-size: 8
          queue-capacity: 100
          keep-alive-seconds: 60
          thread-name-prefix: "LoginLog-Dev-"
          wait-for-tasks-to-complete-on-shutdown: true
          await-termination-seconds: 30

    # dubbo 消费者group
    dubbo.consumer.group.user: user-dev
    dubbo.consumer.group.shop: shop-dev
    dubbo.consumer.group.order: order-dev
    dubbo.consumer.group.reservation: reservation-dev
    dubbo.consumer.group.review: review-dev
    dubbo.consumer.group.marketing: marketing-dev
    dubbo.consumer.group.authentication: authentication-dev