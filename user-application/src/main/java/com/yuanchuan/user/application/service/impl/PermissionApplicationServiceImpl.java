package com.yuanchuan.user.application.service.impl;

import com.yuanchuan.authentication.content.model.UserContext;
import com.yuanchuan.authentication.content.utils.AuthUserContextUtils;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.user.api.dto.PermissionDTO;
import com.yuanchuan.user.api.dto.PermissionSelectDTO;
import com.yuanchuan.user.api.dto.PermissionTreeDTO;
import com.yuanchuan.user.api.request.PermissionCreateRequest;
import com.yuanchuan.user.api.request.PermissionQueryRequest;
import com.yuanchuan.user.api.request.PermissionUpdateRequest;
import com.yuanchuan.user.api.service.PermissionService;
import com.yuanchuan.user.domain.model.Permission;
import com.yuanchuan.user.domain.service.PermissionDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 权限应用服务实现类
 */
@Slf4j
@Service
@DubboService(version = "1.0", group = "${dubbo.group}", delay = -1, retries = -1, timeout = 600000)
public class PermissionApplicationServiceImpl implements PermissionService {

    @Autowired
    private PermissionDomainService permissionDomainService;

    @Override
    public List<PermissionTreeDTO> getUserPermissionTree(Long accountId) {
        List<Permission> permissions = permissionDomainService.getUserPermissionTree(accountId);
        return buildPermissionTree(permissions);
    }

    @Override
    public PageResult<PermissionDTO> queryPermissions(PermissionQueryRequest request) {
        List<Permission> permissions = permissionDomainService.queryPermissions(
                request.getPermissionMenu(),
                request.getStatus(),
                request.getKeyword(),
                request
        );

        Long total = permissionDomainService.countPermissions(
                request.getPermissionMenu(),
                request.getStatus(),
                request.getKeyword()
        );

        List<PermissionDTO> permissionDTOs = permissions.stream()
                .map(this::toPermissionDTO)
                .collect(Collectors.toList());

        return PageResult.<PermissionDTO>builder()
                .records(permissionDTOs)
                .total(total)
                .pageNum(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
    }

    @Override
    public List<PermissionSelectDTO> getPermissionEntries() {
        List<Permission> permissions = permissionDomainService.getPermissionEntries();
        return permissions.stream()
                .map(this::toPermissionSelectDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Long createPermission(PermissionCreateRequest request) {
        UserContext userContext =  AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        Permission permission = new Permission();
        BeanUtils.copyProperties(request, permission);

        Permission savedPermission = permissionDomainService.createPermission(permission,userContext.getUserId(),userContext.getUsername());
        return savedPermission.getId();
    }

    @Override
    public Boolean updatePermission(PermissionUpdateRequest request) {
        UserContext userContext =  AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        Permission permission = new Permission();
        BeanUtils.copyProperties(request, permission);

        permissionDomainService.updatePermission(permission,userContext.getUserId(),userContext.getUsername());
        return true;
    }

    @Override
    public PermissionDTO getPermissionById(Long id) {
        Permission permission = permissionDomainService.getPermissionById(id);
        return toPermissionDTO(permission);
    }

    @Override
    public Boolean deletePermission(Long id) {
        return permissionDomainService.deletePermission(id);
    }

    /**
     * 构建权限树
     */
    private List<PermissionTreeDTO> buildPermissionTree(List<Permission> permissions) {
        Map<Long, List<Permission>> parentMap = permissions.stream()
                .collect(Collectors.groupingBy(p -> p.getParentId() == null ? 0L : p.getParentId()));

        return buildTreeRecursive(parentMap, 0L);
    }

    /**
     * 递归构建权限树
     */
    private List<PermissionTreeDTO> buildTreeRecursive(Map<Long, List<Permission>> parentMap, Long parentId) {
        List<Permission> children = parentMap.get(parentId);
        if (children == null) {
            return new ArrayList<>();
        }

        return children.stream()
                .map(permission -> {
                    PermissionTreeDTO dto = new PermissionTreeDTO();
                    dto.setPermissionId(permission.getId());
                    dto.setPermissionCode(permission.getPermissionCode());
                    dto.setType(permission.getType());
                    dto.setPermissionName(permission.getPermissionName());
                    dto.setUrl(permission.getUrl());
                    dto.setChildren(buildTreeRecursive(parentMap, permission.getId()));
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换为PermissionDTO
     */
    private PermissionDTO toPermissionDTO(Permission permission) {
        PermissionDTO dto = new PermissionDTO();
        BeanUtils.copyProperties(permission, dto);
        dto.setPermissionId(permission.getId());
        return dto;
    }

    /**
     * 转换为PermissionSelectDTO
     */
    private PermissionSelectDTO toPermissionSelectDTO(Permission permission) {
        PermissionSelectDTO dto = new PermissionSelectDTO();
        dto.setId(permission.getId());
        dto.setPermissionName(permission.getPermissionName());
        dto.setPermissionCode(permission.getPermissionCode());
        return dto;
    }
}
