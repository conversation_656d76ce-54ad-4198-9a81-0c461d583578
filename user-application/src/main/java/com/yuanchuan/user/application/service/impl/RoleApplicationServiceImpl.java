package com.yuanchuan.user.application.service.impl;

import com.yuanchuan.authentication.content.model.UserContext;
import com.yuanchuan.authentication.content.utils.AuthUserContextUtils;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.user.api.dto.RoleDTO;
import com.yuanchuan.user.api.dto.RoleSelectDTO;
import com.yuanchuan.user.api.request.RoleCreateRequest;
import com.yuanchuan.user.api.request.RoleQueryRequest;
import com.yuanchuan.user.api.request.RoleUpdateRequest;
import com.yuanchuan.user.api.service.RoleService;
import com.yuanchuan.user.domain.model.Role;
import com.yuanchuan.user.domain.service.RoleDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色应用服务实现类
 */
@Slf4j
@Service
@DubboService(version = "1.0", group = "${dubbo.group}", delay = -1, retries = -1, timeout = 600000)
public class RoleApplicationServiceImpl implements RoleService {

    @Autowired
    private RoleDomainService roleDomainService;

    @Override
    public PageResult<RoleDTO> queryRoles(RoleQueryRequest request) {
        List<Role> roles = roleDomainService.queryRoles(
                request.getRoleId(),
                request.getStatus(),
                request.getKeyword(),
                request
        );

        Long total = roleDomainService.countRoles(
                request.getRoleId(),
                request.getStatus(),
                request.getKeyword()
        );

        List<RoleDTO> roleDTOs = roles.stream()
                .map(this::toRoleDTO)
                .collect(Collectors.toList());

        return PageResult.<RoleDTO>builder()
                .records(roleDTOs)
                .total(total)
                .pageNum(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
    }

    @Override
    public List<RoleSelectDTO> getRoleSelectList() {
        List<Role> roles = roleDomainService.getRoleSelectList();
        return roles.stream()
                .map(this::toRoleSelectDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Long createRole(RoleCreateRequest request) {
        // 获取系统登录用户信息
        UserContext userContext =  AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        Role role = new Role();
        BeanUtils.copyProperties(request, role);

        Role savedRole = roleDomainService.createRole(role, request.getPermissionIds(),userContext.getUsername());
        return savedRole.getId();
    }

    @Override
    public Boolean updateRole(RoleUpdateRequest request) {
        // 获取系统登录用户信息
        UserContext userContext =  AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        Role role = new Role();
        BeanUtils.copyProperties(request, role);

        roleDomainService.updateRole(role, request.getPermissionIds(),userContext.getUserId(),userContext.getUsername());
        return true;
    }

    @Override
    public RoleDTO getRoleById(Long id) {
        Role role = roleDomainService.queryRoleById(id);
        RoleDTO dto = toRoleDTO(role);
        return dto;
    }

    @Override
    public Boolean deleteRole(Long id) {
        // 获取系统登录用户信息
        UserContext userContext =  AuthUserContextUtils.getCurrentUser();
        if(userContext == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        return roleDomainService.deleteRole(id,userContext.getUsername());
    }

    @Override
    public List<RoleDTO> getUserRoles(Long accountId) {
        List<Role> roles = roleDomainService.getUserRoles(accountId);
        return roles.stream()
                .map(this::toRoleDTO)
                .collect(Collectors.toList());
    }

    /**
     * 转换为RoleDTO
     */
    private RoleDTO toRoleDTO(Role role) {
        RoleDTO dto = new RoleDTO();
        BeanUtils.copyProperties(role, dto);

        if(role.getPermissionIds() != null) {
            dto.setPermissionIds(Arrays.stream(role.getPermissionIds().split(","))
                    .map(String::trim)          // 去除空格，防止输入为 "1, 2 ,3"
                    .filter(s -> !s.isEmpty())  // 防止空字符串
                    .map(Long::valueOf)         // 转为 Long 类型
                    .collect(Collectors.toList()));
        }

        if(role.getPermissionGroupDesc() != null) {
            dto.setPermissionNames(Arrays.stream(role.getPermissionGroupDesc().split(";"))
                    .map(String::trim)          // 去除空格，防止输入为 "1, 2 ,3"
                    .filter(s -> !s.isEmpty())  // 防止空字符串
                    //.map(String::valueOf)         // 转为 Long 类型
                    .collect(Collectors.toList()));
        }
        return dto;
    }

    /**
     * 转换为RoleSelectDTO
     */
    private RoleSelectDTO toRoleSelectDTO(Role role) {
        RoleSelectDTO dto = new RoleSelectDTO();
        dto.setRoleId(role.getId());
        dto.setRoleName(role.getRoleName());
        return dto;
    }
}
