package com.yuanchuan.user.application.service.impl;

import com.yuanchuan.common.constant.user.UserVerificationRedisKeys;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.utils.RedisUtil;
import com.yuanchuan.common.utils.ValidationTaiWanPhoneUtil;
import com.yuanchuan.user.api.service.VerificationService;
import com.yuanchuan.user.context.enums.SmsAndEmailBusinessType;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务实现类
 */
@Service
@DubboService(version = "1.0", group = "${dubbo.group}", delay = -1, retries = -1, timeout = 600000)
public class VerificationApplicationServiceImpl implements VerificationService {

    private static final int CODE_EXPIRE_MINUTES = 5; // 验证码有效期5分钟
    private static final int RATE_LIMIT_SECONDS = 60; // 发送频率限制60秒
    private static final int IP_RATE_LIMIT_PER_MINUTE = 10; // 每IP每分钟最多请求10次
    private static final int DAILY_LIMIT = 5; // 每天最多发送5次

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public Long sendSmsCode(String phone, SmsAndEmailBusinessType businessType) {
        // 验证台湾手机号格式
        if (!ValidationTaiWanPhoneUtil.isValidTaiwanPhone(phone)) {
            throw new BusinessException(UsersErrorCode.PHONE_FORMAT_ERROR.getCode(), UsersErrorCode.PHONE_FORMAT_ERROR.getMsg());
        }

        // 默认业务类型
        if (Objects.isNull(businessType)) {
            businessType = SmsAndEmailBusinessType.PHONE_UNBIND_CODE;
        }

        // 生成6位数字验证码
        String code = RandomStringUtils.randomNumeric(6);

        // 根据业务类型构建验证码存储键
        String codeKey = UserVerificationRedisKeys.getSmsCodeKey(businessType.getName(), phone);

        // 检查是否存在未过期的验证码
        String existingCode = redisUtil.get(codeKey);
        if (StringUtils.isNotEmpty(existingCode)) {
            throw new BusinessException(UsersErrorCode.VERIFICATION_CODE_RATE_LIMIT.getCode(), UsersErrorCode.VERIFICATION_CODE_RATE_LIMIT.getMsg());
        }

        // 存储验证码，设置过期时间
        redisUtil.set(codeKey, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        // 设置发送频率限制
        String limitKey = UserVerificationRedisKeys.getSmsLimitKey(phone);
        redisUtil.set(limitKey, "1", RATE_LIMIT_SECONDS, TimeUnit.SECONDS);

        // 增加每日计数器
        String dailyKey = UserVerificationRedisKeys.getSmsDailyCounterKey(phone);
        redisUtil.increment(dailyKey);

        // 如果是当天第一次设置，设置过期时间为当天结束
        if (redisUtil.getExpire(dailyKey, TimeUnit.SECONDS) < 0) {
            // 计算当天剩余秒数
            long expireSeconds = redisUtil.getRemainSecondsInDay();
            redisUtil.expire(dailyKey, expireSeconds, TimeUnit.SECONDS);
        }

        // TODO: 实际发送短信验证码
        // 这里应该调用短信发送服务

        // 返回过期时间戳
        return System.currentTimeMillis() + CODE_EXPIRE_MINUTES * 60 * 1000;
    }

    @Override
    public Long sendEmailCode(String email, SmsAndEmailBusinessType businessType) {
        // 验证邮箱格式
        if (!ValidationTaiWanPhoneUtil.isValidEmail(email)) {
            throw new BusinessException(UsersErrorCode.EMAIL_FORMAT_ERROR.getCode(), UsersErrorCode.EMAIL_FORMAT_ERROR.getMsg());
        }

        // 默认业务类型
        if (Objects.isNull(businessType)) {
            businessType = SmsAndEmailBusinessType.PHONE_UNBIND_CODE;
        }

        // 生成6位数字验证码
        String code = RandomStringUtils.randomNumeric(6);

        // 根据业务类型构建验证码存储键
        String codeKey = UserVerificationRedisKeys.getEmailCodeKey(businessType.getName(), email);

        // 检查是否存在未过期的验证码
        String existingCode = redisUtil.get(codeKey);
        if (StringUtils.isNotEmpty(existingCode)) {
            throw new BusinessException(UsersErrorCode.VERIFICATION_CODE_RATE_LIMIT.getCode(), UsersErrorCode.VERIFICATION_CODE_RATE_LIMIT.getMsg());
        }

        // 存储验证码，设置过期时间
        redisUtil.set(codeKey, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        // 设置发送频率限制
        String limitKey = UserVerificationRedisKeys.getEmailLimitKey(email);
        redisUtil.set(limitKey, "1", RATE_LIMIT_SECONDS, TimeUnit.SECONDS);

        // 增加每日计数器
        String dailyKey = UserVerificationRedisKeys.getEmailDailyCounterKey(email);
        redisUtil.increment(dailyKey);

        // 如果是当天第一次设置，设置过期时间为当天结束
        if (redisUtil.getExpire(dailyKey, TimeUnit.SECONDS) < 0) {
            // 计算当天剩余秒数
            long expireSeconds = redisUtil.getRemainSecondsInDay();
            redisUtil.expire(dailyKey, expireSeconds, TimeUnit.SECONDS);
        }

        // TODO: 实际发送邮箱验证码
        // 这里应该调用邮件发送服务

        // 返回过期时间戳
        return System.currentTimeMillis() + CODE_EXPIRE_MINUTES * 60 * 1000;
    }

    @Override
    public boolean verifySmsCode(String phone, String code, SmsAndEmailBusinessType businessType) {
        // 从 Redis 中获取存储的验证码
        String key = UserVerificationRedisKeys.getSmsCodeKey(businessType.getName(), phone);
        String storedCode = redisUtil.get(key);

        if (storedCode != null && storedCode.equals(code)) {
            // 验证成功后删除验证码
            // redisUtil.delete(key);
            return true;
        }

        return false;
    }

    @Override
    public boolean verifyEmailCode(String email, String code, SmsAndEmailBusinessType businessType) {
        // 从 Redis 中获取存储的验证码
        String key = UserVerificationRedisKeys.getEmailCodeKey(businessType.getName(), email);
        String storedCode = redisUtil.get(key);

        if (storedCode != null && storedCode.equals(code)) {
            // 验证成功后删除验证码
            // redisUtil.delete(key);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkSmsRateLimit(String phone) {
        // 检查发送频率限制
        String limitKey = UserVerificationRedisKeys.getSmsLimitKey(phone);
        if (redisUtil.hasKey(limitKey)) {
            return false;
        }

        // 检查每日发送次数限制
        String dailyKey = UserVerificationRedisKeys.getSmsDailyCounterKey(phone);
        String countStr = redisUtil.get(dailyKey);
        int count = countStr != null ? Integer.parseInt(countStr) : 0;

        return count < DAILY_LIMIT;
    }

    @Override
    public boolean checkEmailRateLimit(String email) {
        // 检查发送频率限制
        String limitKey = UserVerificationRedisKeys.getEmailLimitKey(email);
        if (redisUtil.hasKey(limitKey)) {
            return false;
        }

        // 检查每日发送次数限制
        String dailyKey = UserVerificationRedisKeys.getEmailDailyCounterKey(email);
        String countStr = redisUtil.get(dailyKey);
        int count = countStr != null ? Integer.parseInt(countStr) : 0;

        return count < DAILY_LIMIT;
    }

    @Override
    public boolean checkIpRateLimit(String ip) {
        // 按分钟计算，每分钟生成一个新的key
        long minute = System.currentTimeMillis() / (60 * 1000);
        String key = UserVerificationRedisKeys.getIpLimitKey(ip, minute);
        String countStr = redisUtil.get(key);
        int count = countStr != null ? Integer.parseInt(countStr) : 0;

        // 检查是否超过每分钟限制
        if (count >= IP_RATE_LIMIT_PER_MINUTE) {
            return false;
        }

        // 增加计数
        redisUtil.increment(key);

        // 设置过期时间为1分钟
        if (redisUtil.getExpire(key, TimeUnit.SECONDS) < 0) {
            redisUtil.expire(key, 60, TimeUnit.SECONDS);
        }

        return true;
    }
}
