package com.yuanchuan.user.application.service.impl;

import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.user.api.dto.OperationAccountDTO;
import com.yuanchuan.user.api.request.OperationAccountCreateRequest;
import com.yuanchuan.user.api.request.OperationAccountQueryRequest;
import com.yuanchuan.user.api.request.OperationAccountUpdateRequest;
import com.yuanchuan.user.api.service.OperationAccountService;
import com.yuanchuan.user.application.service.OperationAccountApplicationService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 运营账号管理服务实现
 */
@DubboService
public class OperationAccountServiceImpl implements OperationAccountService {

    @Autowired
    private OperationAccountApplicationService operationAccountApplicationService;

    @Override
    public PageResult<OperationAccountDTO> queryOperationAccounts(OperationAccountQueryRequest request) {
        return operationAccountApplicationService.queryOperationAccounts(request);
    }

    @Override
    public Long createOperationAccount(OperationAccountCreateRequest request) {
        return operationAccountApplicationService.createOperationAccount(request);
    }

    @Override
    public Boolean updateOperationAccount(OperationAccountUpdateRequest request) {
        return operationAccountApplicationService.updateOperationAccount(request);
    }

    @Override
    public OperationAccountDTO getOperationAccountById(Long id) {
        return operationAccountApplicationService.getOperationAccountById(id);
    }
}
